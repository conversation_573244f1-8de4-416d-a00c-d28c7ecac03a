import apiClient from "@/apiClient";
import { executeMappingPlan, generateMappingPlan } from "./fieldMappingService";

/**
 * Custom Fields Processor
 *
 * Handles field mapping and value conversion between AutoPatient (AP) and CliniCore (CC) systems.
 *
 * Features:
 * - Field type mapping and creation
 * - Bidirectional value conversion
 * - TEXTBOX_LIST conversion for multi-value fields
 * - Boolean ↔ RADIO conversion with international support
 * - Medical field handling
 * - Standard field mapping (email, phone)
 */

export async function synchronizeCustomFields(): Promise<{}> {
  const apCustomFields = await apiClient.ap.apCustomfield.allWithParentFilter();
  const ccCustomFields = await apiClient.cc.ccCustomfieldReq.all();

  const { decisions, summary } = generateMappingPlan(
    ccCustomFields,
    apCustomFields
  );
  const execution = await executeMappingPlan(decisions);
  return {
    summary,
    execution,
  };
}

// Export field mapping functionality
export { generateMappingPlan, executeMappingPlan } from "./fieldMappingService";
export { mapCCtoAPField, mapAPtoCCField } from "./mapping";

// Export value conversion system
export * from "./values";

// Export convertAPToCC enhancement tests
export { runConvertAPToCCTests } from "./test-convertAPToCC-enhancements";
