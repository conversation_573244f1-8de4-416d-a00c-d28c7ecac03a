/**
 * Field Mapping Service
 * 
 * Practical service that uses the enhanced mapCCFieldToAPType function
 * to handle field synchronization between CC and AP platforms.
 */

import { mapCCtoAPField } from "./mapping";
import type { GetCCCustomField, APGetCustomFieldType, APPostCustomfieldType } from "@/type";
import { logInfo, logWarn, logDebug, logError } from "@/utils/logger";
import apiClient from "@/apiClient";
import { getDb, dbSchema } from "@/database";
import { eq } from "drizzle-orm";

/**
 * Database service functions for field mapping queries
 */

/**
 * Get field mapping by CC field ID
 */
export async function getFieldMappingByCCId(ccId: number): Promise<{
	ccConfig: GetCCCustomField;
	apConfig: APGetCustomFieldType | null;
	mappingType: string;
} | null> {
	const db = getDb();

	try {
		const mappings = await db
			.select()
			.from(dbSchema.customFields)
			.where(eq(dbSchema.customFields.ccId, ccId))
			.limit(1);

		if (mappings.length === 0) {
			logWarn("No field mapping found for CC field ID", { ccId });
			return null;
		}

		const mapping = mappings[0];

		if (!mapping.ccConfig) {
			logError("Field mapping found but ccConfig is missing", { ccId, mappingId: mapping.id });
			return null;
		}

		return {
			ccConfig: mapping.ccConfig,
			apConfig: mapping.apConfig,
			mappingType: mapping.mappingType || "custom_to_custom"
		};
	} catch (error) {
		logError("Failed to query field mapping by CC ID", { ccId, error });
		return null;
	}
}

/**
 * Get field mapping by AP field ID
 */
export async function getFieldMappingByAPId(apId: string): Promise<{
	ccConfig: GetCCCustomField | null;
	apConfig: APGetCustomFieldType;
	mappingType: string;
} | null> {
	const db = getDb();

	try {
		const mappings = await db
			.select()
			.from(dbSchema.customFields)
			.where(eq(dbSchema.customFields.apId, apId))
			.limit(1);

		if (mappings.length === 0) {
			logWarn("No field mapping found for AP field ID", { apId });
			return null;
		}

		const mapping = mappings[0];

		if (!mapping.apConfig) {
			logError("Field mapping found but apConfig is missing", { apId, mappingId: mapping.id });
			return null;
		}

		return {
			ccConfig: mapping.ccConfig,
			apConfig: mapping.apConfig,
			mappingType: mapping.mappingType || "custom_to_custom"
		};
	} catch (error) {
		logError("Failed to query field mapping by AP ID", { apId, error });
		return null;
	}
}

interface FieldMappingDecision {
	action: "map_to_standard" | "map_to_custom" | "create_custom" | "skip";
	sourceField: GetCCCustomField;
	targetField?: string; // For standard field mappings
	targetCustomField?: APGetCustomFieldType; // For custom field mappings
	confidence: number;
	reason: string;
	notes?: string;
}

/**
 * Analyze a CC field and determine the best mapping strategy for AP
 */
function analyzeFieldMapping(
	ccField: GetCCCustomField,
	apCustomFields: APGetCustomFieldType[]
): FieldMappingDecision {
	// Use enhanced mapping to get comprehensive analysis with auto-extracted field info
	const enhancedResult = mapCCtoAPField(ccField);



	// Case 1: Standard field mapping detected
	if (enhancedResult.mappingType === "standard_field" && enhancedResult.standardFieldMapping) {
		return {
			action: "map_to_standard",
			sourceField: ccField,
			targetField: enhancedResult.standardFieldMapping.targetField,
			confidence: enhancedResult.confidence,
			reason: `CC custom field '${ccField.name}' maps to AP standard field '${enhancedResult.standardFieldMapping.targetField}'`,
			notes: enhancedResult.standardFieldMapping.notes
		};
	}

	// Case 2: Custom field mapping - look for existing AP custom field
	if (enhancedResult.mappingType === "custom_field" && enhancedResult.customFieldType) {
		// Try to find matching AP custom field
		const matchingAPField = findMatchingAPCustomField(ccField, apCustomFields, enhancedResult.customFieldType);
		
		if (matchingAPField) {
			return {
				action: "map_to_custom",
				sourceField: ccField,
				targetCustomField: matchingAPField,
				confidence: enhancedResult.confidence,
				reason: `CC custom field '${ccField.name}' maps to existing AP custom field '${matchingAPField.name}'`
			};
		}

		// No matching AP custom field found - suggest creation
		return {
			action: "create_custom",
			sourceField: ccField,
			confidence: enhancedResult.confidence,
			reason: `No matching AP custom field found for '${ccField.name}'. Suggest creating new ${enhancedResult.customFieldType} field.`
		};
	}

	// Fallback case
	return {
		action: "skip",
		sourceField: ccField,
		confidence: 0,
		reason: "Unable to determine appropriate mapping strategy"
	};
}

/**
 * Find matching AP custom field using enhanced type information
 */
function findMatchingAPCustomField(
	ccField: GetCCCustomField,
	apCustomFields: APGetCustomFieldType[],
	expectedType: string
): APGetCustomFieldType | null {
	return apCustomFields.find(apField => {
		// Type must match
		if (apField.dataType !== expectedType) return false;

		// Name matching (using simple string comparison for now)
		const nameMatch = apField.name.toLowerCase() === ccField.name.toLowerCase() ||
						  apField.name.toLowerCase() === ccField.label.toLowerCase();

		// Field key matching (if available)
		const keyMatch = apField.fieldKey?.split('.').pop()?.toLowerCase() === ccField.name.toLowerCase();

		return nameMatch || keyMatch;
	}) || null;
}

/**
 * Process multiple CC fields and generate mapping decisions
 */
export function generateMappingPlan(
	ccFields: GetCCCustomField[],
	apCustomFields: APGetCustomFieldType[]
): {
	decisions: FieldMappingDecision[];
	summary: {
		standardMappings: number;
		customMappings: number;
		newFieldsNeeded: number;
		skipped: number;
	};
} {
	const decisions = ccFields.map(ccField => analyzeFieldMapping(ccField, apCustomFields));

	const summary = {
		standardMappings: decisions.filter(d => d.action === "map_to_standard").length,
		customMappings: decisions.filter(d => d.action === "map_to_custom").length,
		newFieldsNeeded: decisions.filter(d => d.action === "create_custom").length,
		skipped: decisions.filter(d => d.action === "skip").length
	};

	logInfo("Field mapping plan generated", {
		totalFields: ccFields.length,
		...summary
	});

	return { decisions, summary };
}

/**
 * Execute field mapping decisions - actually creates fields and stores mappings
 */
export async function executeMappingPlan(decisions: FieldMappingDecision[]): Promise<{
	executed: number;
	failed: number;
	skipped: number;
	results: Array<{
		decision: FieldMappingDecision;
		success: boolean;
		error?: string;
		createdField?: APGetCustomFieldType;
	}>;
}> {
	const results = [];
	let executed = 0;
	let failed = 0;
	let skipped = 0;

	for (const decision of decisions) {
		try {
			switch (decision.action) {
				case "map_to_standard":
					logInfo(`✅ Standard field mapping: ${decision.sourceField.name} → ${decision.targetField}`);
					// Store the standard field mapping in database for future value sync
					await storeFieldMapping(decision, "standard_field");
					results.push({ decision, success: true });
					executed++;
					break;

				case "map_to_custom":
					logInfo(`🔗 Custom field mapping: ${decision.sourceField.name} → ${decision.targetCustomField?.name}`);
					// Store the custom field mapping in database for future value sync
					await storeFieldMapping(decision, "custom_field");
					results.push({ decision, success: true });
					executed++;
					break;

				case "create_custom":
					logInfo(`🆕 Creating AP custom field for: ${decision.sourceField.name}`);
					try {
						// Actually create the AP custom field
						const createdField = await createAPCustomField(decision.sourceField);

						// Store the new field mapping (this won't throw errors, just logs warnings)
						await storeFieldMapping({
							...decision,
							targetCustomField: createdField
						}, "custom_field");

						results.push({ decision, success: true, createdField });
						executed++;
					} catch (fieldCreationError) {
						// If field creation fails, we don't need to rollback anything
						const errorMessage = fieldCreationError instanceof Error
							? fieldCreationError.message
							: String(fieldCreationError);

						logError(`❌ Failed to create AP custom field for ${decision.sourceField.name}`, {
							ccFieldId: decision.sourceField.id,
							ccFieldName: decision.sourceField.name,
							ccFieldType: decision.sourceField.type,
							error: errorMessage
						});

						results.push({
							decision,
							success: false,
							error: `Field creation failed: ${errorMessage}`
						});
						failed++;
					}
					break;

				case "skip":
					logWarn(`⏭️ Skipping field: ${decision.sourceField.name} - ${decision.reason}`);
					results.push({ decision, success: true });
					skipped++;
					break;
			}
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : String(error);
			logWarn(`❌ Failed to execute mapping for ${decision.sourceField.name}: ${errorMessage}`);
			results.push({ decision, success: false, error: errorMessage });
			failed++;
		}
	}

	logInfo("Field mapping execution completed", {
		total: decisions.length,
		executed,
		failed,
		skipped
	});

	return { executed, failed, skipped, results };
}

/**
 * Build AP custom field configuration based on field type and CC field data
 */
function buildAPFieldConfiguration(
	ccField: GetCCCustomField,
	fieldType: string
): APPostCustomfieldType {
	// Sanitize field name - replace non-alphanumeric characters with spaces
	const sanitizedName = ccField.label || ccField.name.replace(/[^a-zA-Z0-9]/g, ' ');

	const baseConfig: APPostCustomfieldType = {
		name: sanitizedName,
		dataType: fieldType,
		// Explicitly set model for contact custom fields created via location-specific endpoint
		model: "contact",
	};

	// Handle field types that require special configuration
	switch (fieldType) {
		case "TEXTBOX_LIST":
			// TEXTBOX_LIST fields need a non-empty textBoxListOptions array
			if (ccField.allowedValues?.length > 0) {
				// Use CC allowedValues as labels, but do not prefill values by default
				const mapped = ccField.allowedValues
					.map((av, idx) => ({
						label: `Value ${idx + 1}`,
						prefillValue: av.value || "",
					}))
					.filter(opt => opt.label.trim().length > 0);
				if (mapped.length > 0) {
					baseConfig.textBoxListOptions = mapped;
				} else {
					baseConfig.textBoxListOptions = [{ label: "Value 1", prefillValue: "" }];
				}
			} else {
				// Provide a minimal default to satisfy AP API requirement
				baseConfig.textBoxListOptions = [{ label: "Value 1", prefillValue: "" }];
			}
			break;

		case "MULTIPLE_OPTIONS":
		case "SINGLE_OPTIONS":
		case "RADIO":
			// These field types need options array
			if (ccField.allowedValues?.length > 0) {
				// Use simple string array format for location-specific endpoint
				baseConfig.options = ccField.allowedValues.map(av => av.value);
			}
			break;

		case "CHECKBOX":
			// Checkbox fields also need options
			if (ccField.allowedValues?.length > 0) {
				baseConfig.options = ccField.allowedValues.map(av => av.value);
			}
			break;

		// Other field types (TEXT, LARGE_TEXT, NUMERICAL, etc.) don't need special configuration
		default:
			break;
	}

	return baseConfig;
}

/**
 * Create an AP custom field based on CC field definition
 */
async function createAPCustomField(ccField: GetCCCustomField): Promise<APGetCustomFieldType> {
	// Validate input
	if (!ccField) {
		throw new Error("CC field data is required for AP custom field creation");
	}

	if (!ccField.name || !ccField.label) {
		throw new Error(`CC field must have both name and label. Field: ${JSON.stringify(ccField)}`);
	}

	// Map CC field to AP field type
	const mappingResult = mapCCtoAPField(ccField);

	if (mappingResult.mappingType !== "custom_field" || !mappingResult.customFieldType) {
		throw new Error(`Cannot create AP custom field for ${ccField.name}: not a custom field mapping`);
	}

	// Build the AP custom field configuration with proper type-specific options
	const apFieldData = buildAPFieldConfiguration(ccField, mappingResult.customFieldType);

	logDebug("Creating AP custom field", {
		ccFieldName: ccField.name,
		ccFieldType: ccField.type,
		ccFieldAllowMultipleValues: ccField.allowMultipleValues,
		mappedAPFieldType: mappingResult.customFieldType,
		apFieldData,
		hasAllowedValues: ccField.allowedValues?.length > 0,
		allowedValuesCount: ccField.allowedValues?.length || 0
	});

	try {
		// Create the field using the API client
		const createdField = await apiClient.ap.apCustomfield.create(apFieldData);

		logInfo(`✅ Created AP custom field: ${createdField.name} (${createdField.dataType})`, {
			apFieldId: createdField.id,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
			ccFieldType: ccField.type,
			apFieldType: createdField.dataType,
			hasOptions: !!(createdField.picklistOptions || createdField.textBoxListOptions)
		});

		return createdField;
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logError(`❌ Failed to create AP custom field for CC field '${ccField.name}'`, {
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
			ccFieldType: ccField.type,
			mappedAPFieldType: mappingResult.customFieldType,
			apFieldData,
			error: errorMessage
		});
		throw new Error(`Failed to create AP custom field for '${ccField.name}': ${errorMessage}`);
	}
}

/**
 * Store field mapping in database for future value synchronization
 */
async function storeFieldMapping(
	decision: FieldMappingDecision,
	mappingType: "standard_field" | "custom_field"
): Promise<void> {
	if (!decision.sourceField) {
		throw new Error("Source field is required for storing field mapping");
	}

	// Map function parameter to database enum values
	const dbMappingType = mappingType === "standard_field"
		? "custom_to_standard"
		: "custom_to_custom";

	const db = getDb();

	try {
		// Prepare the database record
		const mappingRecord = {
			ccId: decision.sourceField.id,
			apId: decision.targetCustomField?.id || null,
			name: decision.sourceField.name,
			label: decision.sourceField.label,
			type: decision.sourceField.type,
			ccConfig: decision.sourceField,
			apConfig: decision.targetCustomField || null,
			mappingType: dbMappingType,
			// Store standard field mappings in appropriate columns
			apStandardField: mappingType === "standard_field" ? decision.targetField : null,
			ccStandardField: null, // This would be used for AP→CC standard field mappings
		};

		logDebug("Storing field mapping in database", {
			ccFieldId: decision.sourceField.id,
			ccFieldName: decision.sourceField.name,
			mappingType,
			dbMappingType,
			targetField: decision.targetField,
			targetCustomFieldId: decision.targetCustomField?.id,
			targetCustomFieldName: decision.targetCustomField?.name,
			confidence: decision.confidence,
			notes: decision.notes
		});

		// Check if mapping already exists
		const existingMapping = await db
			.select()
			.from(dbSchema.customFields)
			.where(eq(dbSchema.customFields.ccId, decision.sourceField.id))
			.limit(1);

		if (existingMapping.length > 0) {
			// Update existing mapping
			await db
				.update(dbSchema.customFields)
				.set({
					...mappingRecord,
					updatedAt: new Date()
				})
				.where(eq(dbSchema.customFields.ccId, decision.sourceField.id));

			logInfo("📝 Updated existing field mapping in database", {
				mappingId: existingMapping[0].id,
				ccFieldId: decision.sourceField.id,
				ccFieldName: decision.sourceField.name,
				mappingType: dbMappingType
			});
		} else {
			// Insert new mapping
			const insertedMappings = await db
				.insert(dbSchema.customFields)
				.values(mappingRecord)
				.returning();

			logInfo("📝 Stored new field mapping in database", {
				mappingId: insertedMappings[0]?.id,
				ccFieldId: decision.sourceField.id,
				ccFieldName: decision.sourceField.name,
				mappingType: dbMappingType
			});
		}

	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logError(`❌ Failed to store field mapping in database`, {
			ccFieldId: decision.sourceField.id,
			ccFieldName: decision.sourceField.name,
			mappingType,
			dbMappingType,
			error: errorMessage
		});

		// Don't throw the error - log it but continue execution
		// This prevents database issues from breaking the field creation process
		logWarn("⚠️ Continuing execution despite database storage failure", {
			ccFieldName: decision.sourceField.name,
			reason: "Database storage is not critical for field creation"
		});
	}
}
