/**
 * Field Value Conversion Examples
 * 
 * This file demonstrates the usage of the field value conversion system
 * with practical examples from the field-conversion-specification.md.
 */

import type {
  GetCC<PERSON>ustomField,
  GetCCPatientCustomField,
  APGetCustomFieldType
} from "@/type";
import {
  FieldValueConversionService,
  convertCCToAP,
  convertAPToCC,
  createConversionService,
  getRecommendedAPFieldType,
  getRecommendedCCFieldType,
  createBooleanRadioFieldConfig,
  createTextboxListFieldConfig
} from "./index";

/**
 * Example 1: CC Multi-Value Text → AP TEXTBOX_LIST
 * 
 * This example demonstrates converting a CC multi-value text field (allergies)
 * to an AP TEXTBOX_LIST field according to the specification.
 */
export async function example1_CCMultiValueToAPTextboxList() {
  // Source CC field
  const ccField: GetCCCustomField = {
    id: 123,
    name: "allergies",
    label: "Patient Allergies",
    type: "text",
    allowMultipleValues: true,
    validation: "",
    color: null,
    positions: [],
    useCustomSort: null,
    isRequired: false,
    allowedValues: [
      { id: 1, value: "Peanuts", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
      { id: 2, value: "Shellfish", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
      { id: 3, value: "Latex", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null }
    ],
    defaultValues: []
  };

  // CC patient field with values
  const ccPatientField: GetCCPatientCustomField = {
    id: 456,
    values: [
      { id: 1, value: "Peanuts", createdAt: "2024-01-01", updatedAt: "2024-01-01", createdBy: 1, updatedBy: 1 },
      { id: 2, value: "Shellfish", createdAt: "2024-01-01", updatedAt: "2024-01-01", createdBy: 1, updatedBy: 1 },
      { id: 3, value: "Latex", createdAt: "2024-01-01", updatedAt: "2024-01-01", createdBy: 1, updatedBy: 1 }
    ],
    field: ccField,
    patient: 789
  };

  // Target AP TEXTBOX_LIST field
  const apField: APGetCustomFieldType = {
    id: "ap_field_456",
    name: "Allergies",
    dataType: "TEXTBOX_LIST",
    picklistOptions: [
      { id: "opt1", label: "Peanuts", prefillValue: "Peanuts" },
      { id: "opt2", label: "Shellfish", prefillValue: "Shellfish" },
      { id: "opt3", label: "Latex", prefillValue: "Latex" }
    ],
    textBoxListOptions: [
      { label: "Value 1", prefillValue: "Peanuts" },
      { label: "Value 2", prefillValue: "Shellfish" },
      { label: "Value 3", prefillValue: "Latex" }
    ]
  };

  // Perform conversion using new simplified API
  const service = createConversionService();
  const result = await service.convertCCToAP(ccPatientField);

  console.log("Example 1 - CC Multi-Value to AP TEXTBOX_LIST:");
  console.log("Result:", JSON.stringify(result, null, 2));
  
  // Expected result:
  // {
  //   success: true,
  //   value: {
  //     id: "ap_field_456",
  //     field_value: {
  //       "opt1": "Peanuts",
  //       "opt2": "Shellfish",
  //       "opt3": "Latex"
  //     }
  //   }
  // }

  return result;
}

/**
 * Example 2: CC Boolean → AP RADIO
 * 
 * This example demonstrates converting a CC boolean field (smoker status)
 * to an AP RADIO field with international Yes/Ja/No/Nein options.
 */
export async function example2_CCBooleanToAPRadio() {
  // Source CC boolean field
  const ccField: GetCCCustomField = {
    id: 789,
    name: "smoker",
    label: "Is Patient a Smoker?",
    type: "boolean",
    allowMultipleValues: false,
    validation: "",
    color: null,
    positions: [],
    useCustomSort: null,
    isRequired: false,
    allowedValues: [],
    defaultValues: []
  };

  // CC patient field with boolean value
  const ccPatientField: GetCCPatientCustomField = {
    id: 101,
    values: [
      { id: 1, value: "true", createdAt: "2024-01-01", updatedAt: "2024-01-01", createdBy: 1, updatedBy: 1 }
    ],
    field: ccField,
    patient: 789
  };

  // Target AP RADIO field
  const apField: APGetCustomFieldType = {
    id: "ap_field_101",
    name: "Smoker",
    dataType: "RADIO",
    picklistOptions: ["Yes", "Ja", "No", "Nein"]
  };

  // Perform conversion using new simplified API
  const service = createConversionService();
  const result = await service.convertCCToAP(ccPatientField);

  console.log("Example 2 - CC Boolean to AP RADIO:");
  console.log("Result:", JSON.stringify(result, null, 2));
  
  // Expected result:
  // {
  //   success: true,
  //   value: {
  //     id: "ap_field_101",
  //     value: "Yes"
  //   }
  // }

  return result;
}

/**
 * Example 3: CC Multi-Value Medications → AP TEXTBOX_LIST
 * 
 * This example demonstrates converting CC multi-value medication field
 * to AP TEXTBOX_LIST according to the medical field handling rules.
 */
export async function example3_CCMedicationsToAPTextboxList() {
  // Source CC medication field
  const ccField: GetCCCustomField = {
    id: 202,
    name: "medications",
    label: "Current Medications",
    type: "medication",
    allowMultipleValues: true,
    validation: "",
    color: null,
    positions: [],
    useCustomSort: null,
    isRequired: false,
    allowedValues: [],
    defaultValues: []
  };

  // CC patient field with medication values
  const ccPatientField: GetCCPatientCustomField = {
    id: 303,
    values: [
      { id: 1, value: "Aspirin 81mg", createdAt: "2024-01-01", updatedAt: "2024-01-01", createdBy: 1, updatedBy: 1 },
      { id: 2, value: "Lisinopril 10mg", createdAt: "2024-01-01", updatedAt: "2024-01-01", createdBy: 1, updatedBy: 1 },
      { id: 3, value: "Metformin 500mg", createdAt: "2024-01-01", updatedAt: "2024-01-01", createdBy: 1, updatedBy: 1 }
    ],
    field: ccField,
    patient: 789
  };

  // Target AP TEXTBOX_LIST field
  const apField: APGetCustomFieldType = {
    id: "ap_field_303",
    name: "Current Medications",
    dataType: "TEXTBOX_LIST",
    picklistOptions: [
      { id: "opt1", label: "Aspirin 81mg", prefillValue: "Aspirin 81mg" },
      { id: "opt2", label: "Lisinopril 10mg", prefillValue: "Lisinopril 10mg" },
      { id: "opt3", label: "Metformin 500mg", prefillValue: "Metformin 500mg" }
    ],
    textBoxListOptions: [
      { label: "Medication 1", prefillValue: "Aspirin 81mg" },
      { label: "Medication 2", prefillValue: "Lisinopril 10mg" },
      { label: "Medication 3", prefillValue: "Metformin 500mg" }
    ]
  };

  // Perform conversion using new simplified API
  const service = createConversionService();
  const result = await service.convertCCToAP(ccPatientField);

  console.log("Example 3 - CC Medications to AP TEXTBOX_LIST:");
  console.log("Result:", JSON.stringify(result, null, 2));

  return result;
}

/**
 * Example 4: Field Type Recommendations
 * 
 * This example demonstrates how to get recommended field types
 * for optimal conversion between platforms.
 */
export function example4_FieldTypeRecommendations() {
  console.log("Example 4 - Field Type Recommendations:");

  // CC field examples
  const ccFields = [
    { id: 1, name: "allergies", type: "text", allowMultipleValues: true },
    { id: 2, name: "smoker", type: "boolean", allowMultipleValues: false },
    { id: 3, name: "notes", type: "textarea", allowMultipleValues: false },
    { id: 4, name: "conditions", type: "select", allowMultipleValues: true }
  ];

  ccFields.forEach(field => {
    const recommendedType = getRecommendedAPFieldType(field as GetCCCustomField);
    console.log(`CC ${field.type} (multi: ${field.allowMultipleValues}) → AP ${recommendedType}`);
  });

  // AP field examples
  const apFields = [
    { id: "1", name: "Notes", dataType: "TEXTBOX_LIST" },
    { id: "2", name: "Status", dataType: "RADIO" },
    { id: "3", name: "Categories", dataType: "MULTIPLE_OPTIONS" },
    { id: "4", name: "Phone", dataType: "PHONE" }
  ];

  apFields.forEach(field => {
    const recommendation = getRecommendedCCFieldType(field as APGetCustomFieldType);
    console.log(`AP ${field.dataType} → CC ${recommendation.type} (multi: ${recommendation.allowMultipleValues})`);
  });
}

/**
 * Example 5: Creating Field Configurations
 * 
 * This example demonstrates how to create field configurations
 * for boolean and TEXTBOX_LIST fields.
 */
export function example5_CreateFieldConfigurations() {
  console.log("Example 5 - Creating Field Configurations:");

  // Create boolean RADIO field configuration
  const booleanConfig = createBooleanRadioFieldConfig("Smoker Status", {
    defaultYesValue: "Yes",
    defaultNoValue: "No"
  });
  console.log("Boolean RADIO config:", JSON.stringify(booleanConfig, null, 2));

  // Create TEXTBOX_LIST field configuration
  const allergies = ["Peanuts", "Shellfish", "Latex", "Dairy", "Eggs"];
  const textboxConfig = createTextboxListFieldConfig("Patient Allergies", allergies, {
    maxOptions: 10,
    allowEmptyValues: false
  });
  console.log("TEXTBOX_LIST config:", JSON.stringify(textboxConfig, null, 2));
}

/**
 * Run all examples
 */
export async function runAllExamples() {
  console.log("=== Field Value Conversion Examples ===\n");

  try {
    await example1_CCMultiValueToAPTextboxList();
    console.log("\n");

    await example2_CCBooleanToAPRadio();
    console.log("\n");

    await example3_CCMedicationsToAPTextboxList();
    console.log("\n");

    example4_FieldTypeRecommendations();
    console.log("\n");

    example5_CreateFieldConfigurations();
    console.log("\n");

    console.log("=== All examples completed successfully ===");
  } catch (error) {
    console.error("Error running examples:", error);
  }
}

// Export individual examples for testing
export {
  example1_CCMultiValueToAPTextboxList as ccMultiValueToTextboxList,
  example2_CCBooleanToAPRadio as ccBooleanToRadio,
  example3_CCMedicationsToAPTextboxList as ccMedicationsToTextboxList,
  example4_FieldTypeRecommendations as fieldTypeRecommendations,
  example5_CreateFieldConfigurations as createFieldConfigurations
};
