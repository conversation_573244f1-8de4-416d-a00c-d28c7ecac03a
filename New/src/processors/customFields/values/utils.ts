/**
 * Utility Functions for Field Value Conversion
 * 
 * This module provides utility functions for option mapping, standard field
 * handling, and other conversion support functions.
 */

import type {
  OptionMapping,
  StandardFieldMapping,
  ConversionResult,
  ConversionError,
  CCFieldValue,
  APFieldType,
  CCFieldType
} from "./types";
import { ConversionErrorType } from "./types";
import type { GetCCCustomField, APGetCustomFieldType } from "@/type";
import { normalizePhone, normalizeEmail, normalizeDate } from "./validation";

/**
 * Standard field mappings between CC and AP
 */
export const STANDARD_FIELD_MAPPINGS: StandardFieldMapping[] = [
  {
    sourceField: "phone",
    targetField: "phone",
    sourcePlatform: "cc",
    targetPlatform: "ap",
    conversionType: "normalized",
    notes: "CC custom field 'phone' maps to AP standard field 'phone'"
  },
  {
    sourceField: "phone-mobile",
    targetField: "phone",
    sourcePlatform: "cc",
    targetPlatform: "ap",
    conversionType: "normalized",
    notes: "CC custom field 'phone-mobile' maps to AP standard field 'phone'"
  },
  {
    sourceField: "phoneMobile",
    targetField: "phone",
    sourcePlatform: "cc",
    targetPlatform: "ap",
    conversionType: "normalized",
    notes: "CC custom field 'phoneMobile' maps to AP standard field 'phone'"
  },
  {
    sourceField: "email",
    targetField: "email",
    sourcePlatform: "cc",
    targetPlatform: "ap",
    conversionType: "normalized",
    notes: "CC custom field 'email' maps to AP standard field 'email'"
  },
  {
    sourceField: "phone",
    targetField: "phone",
    sourcePlatform: "ap",
    targetPlatform: "cc",
    conversionType: "normalized",
    notes: "AP standard field 'phone' maps to CC custom field 'phone'"
  },
  {
    sourceField: "email",
    targetField: "email",
    sourcePlatform: "ap",
    targetPlatform: "cc",
    conversionType: "normalized",
    notes: "AP standard field 'email' maps to CC custom field 'email'"
  }
];

/**
 * Find standard field mapping for a given field name and direction
 */
export function findStandardFieldMapping(
  fieldName: string,
  sourcePlatform: "cc" | "ap",
  targetPlatform: "ap" | "cc"
): StandardFieldMapping | null {
  const normalizedName = fieldName.toLowerCase();
  
  return STANDARD_FIELD_MAPPINGS.find(mapping => 
    mapping.sourceField.toLowerCase() === normalizedName &&
    mapping.sourcePlatform === sourcePlatform &&
    mapping.targetPlatform === targetPlatform
  ) || null;
}

/**
 * Create option mappings between CC allowedValues and AP picklistOptions
 */
export function createOptionMappings(
  ccField: GetCCCustomField,
  apField: APGetCustomFieldType
): OptionMapping[] {
  const mappings: OptionMapping[] = [];
  
  if (!ccField.allowedValues || !apField.picklistOptions) {
    return mappings;
  }

  const ccValues = ccField.allowedValues.map(av => av.value);
  const apOptions = Array.isArray(apField.picklistOptions) 
    ? apField.picklistOptions 
    : [];

  // Create mappings based on exact matches first
  ccValues.forEach(ccValue => {
    const normalizedCCValue = ccValue.toLowerCase().trim();
    
    // Find exact match in AP options
    const exactMatch = apOptions.find(option => {
      if (typeof option === "string") {
        return option.toLowerCase().trim() === normalizedCCValue;
      } else if (typeof option === "object" && option.label) {
        return option.label.toLowerCase().trim() === normalizedCCValue;
      }
      return false;
    });

    if (exactMatch) {
      const targetValue = typeof exactMatch === "string" ? exactMatch : exactMatch.label;
      const targetId = typeof exactMatch === "string" ? undefined : exactMatch.id;
      
      mappings.push({
        sourceValue: ccValue,
        targetValue,
        targetId,
        confidence: 1.0
      });
    } else {
      // Create fuzzy match or new option
      mappings.push({
        sourceValue: ccValue,
        targetValue: ccValue, // Use original value
        confidence: 0.8 // Lower confidence for new options
      });
    }
  });

  return mappings;
}

/**
 * Generate TEXTBOX_LIST options from CC field values
 */
export function generateTextboxListOptions(
  ccValues: CCFieldValue[],
  maxOptions: number = 100
): Array<{ label: string; prefillValue: string }> {
  const options: Array<{ label: string; prefillValue: string }> = [];
  
  const uniqueValues = Array.from(new Set(
    ccValues
      .map(v => v.value)
      .filter(v => v && v.trim() !== "")
      .slice(0, maxOptions)
  ));

  uniqueValues.forEach((value, index) => {
    if (value) {
      options.push({
        label: `Value ${index + 1}`,
        prefillValue: value
      });
    }
  });

  return options;
}

/**
 * Convert standard field value with appropriate normalization
 */
export function convertStandardFieldValue(
  value: string,
  fieldType: "phone" | "email" | "date"
): ConversionResult<string> {
  try {
    if (!value || value.trim() === "") {
      return {
        success: true,
        value: "",
        warnings: ["Empty standard field value"]
      };
    }

    let normalizedValue: string;
    
    switch (fieldType) {
      case "phone":
        normalizedValue = normalizePhone(value);
        break;
      case "email":
        normalizedValue = normalizeEmail(value);
        break;
      case "date":
        normalizedValue = normalizeDate(value);
        break;
      default:
        normalizedValue = value.trim();
    }

    return {
      success: true,
      value: normalizedValue,
      metadata: {
        originalValue: value,
        conversionType: `standard_field_${fieldType}`
      }
    };

  } catch (error) {
    return {
      success: false,
      error: {
        type: ConversionErrorType.VALIDATION_FAILED,
        message: `Failed to normalize ${fieldType} value: ${error instanceof Error ? error.message : String(error)}`,
        fieldId: "",
        fieldName: fieldType,
        sourceValue: value,
        recoverable: true,
        suggestions: [`Check ${fieldType} format`, "Ensure value is valid"]
      }
    };
  }
}

/**
 * Generate unique option IDs for TEXTBOX_LIST fields
 */
export function generateOptionIds(count: number, prefix: string = "opt"): string[] {
  return Array.from({ length: count }, (_, i) => `${prefix}_${i + 1}`);
}

/**
 * Check if a field supports multi-value conversion
 */
export function supportsMultiValue(fieldType: APFieldType | CCFieldType): boolean {
  const multiValueTypes = [
    "TEXTBOX_LIST",
    "MULTIPLE_OPTIONS", 
    "CHECKBOX",
    "select" // CC select with allowMultipleValues=true
  ];
  
  return multiValueTypes.includes(fieldType);
}

/**
 * Get field type priority for conversion decisions
 */
export function getFieldTypePriority(fieldType: APFieldType): number {
  const priorities: Record<APFieldType, number> = {
    "MULTIPLE_OPTIONS": 10,  // Highest priority for multi-select
    "SINGLE_OPTIONS": 9,
    "TEXTBOX_LIST": 8,
    "RADIO": 7,
    "CHECKBOX": 6,
    "EMAIL": 5,
    "PHONE": 5,
    "DATE": 4,
    "NUMERICAL": 3,
    "LARGE_TEXT": 2,
    "TEXT": 1,               // Lowest priority (fallback)
    "MONETORY": 1,
    "FILE_UPLOAD": 0        // Not supported in CC
  };
  
  return priorities[fieldType] || 0;
}

/**
 * Create reference field value for cross-platform links
 */
export function createReferenceFieldValue(
  entityId: string | number,
  platform: "cc" | "ap",
  fieldType: "PatientID" | "CC Profile" | "AP Profile"
): ConversionResult<string> {
  try {
    let value: string;
    
    switch (fieldType) {
      case "PatientID":
        value = String(entityId);
        break;
      case "CC Profile":
        value = `https://ccdemo.clinicore.eu/patients/${entityId}/timeline`;
        break;
      case "AP Profile":
        // This would need the actual AP contact URL format
        value = `AP Contact ID: ${entityId}`;
        break;
      default:
        throw new Error(`Unknown reference field type: ${fieldType}`);
    }

    return {
      success: true,
      value,
      metadata: {
        originalValue: entityId,
        conversionType: `reference_field_${fieldType.toLowerCase().replace(" ", "_")}`
      }
    };

  } catch (error) {
    return {
      success: false,
      error: {
        type: ConversionErrorType.INVALID_VALUE_FORMAT,
        message: `Failed to create reference field value: ${error instanceof Error ? error.message : String(error)}`,
        fieldId: "",
        fieldName: fieldType,
        sourceValue: entityId,
        recoverable: true
      }
    };
  }
}

/**
 * Merge conversion results for batch operations
 */
export function mergeConversionResults(
  results: ConversionResult[]
): ConversionResult<unknown[]> {
  const values: unknown[] = [];
  const warnings: string[] = [];
  const errors: ConversionError[] = [];
  let hasFailures = false;

  results.forEach((result, index) => {
    if (result.success) {
      values.push(result.value);
      if (result.warnings) {
        warnings.push(...result.warnings.map(w => `Result ${index + 1}: ${w}`));
      }
    } else {
      hasFailures = true;
      if (result.error) {
        errors.push(result.error);
      }
    }
  });

  if (hasFailures && errors.length > 0) {
    return {
      success: false,
      error: errors[0], // Return first error
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }

  return {
    success: true,
    value: values,
    warnings: warnings.length > 0 ? warnings : undefined,
    metadata: {
      conversionType: "batch_merge",
      optionsMapped: values.length,
      optionsSkipped: results.length - values.length
    }
  };
}

/**
 * Clean and validate field name for mapping
 */
export function cleanFieldName(name: string): string {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9\-_]/g, "")
    .replace(/\s+/g, "-");
}

/**
 * Check if two field values are equivalent
 */
export function areValuesEquivalent(
  value1: unknown,
  value2: unknown,
  fieldType: APFieldType | CCFieldType
): boolean {
  // Handle null/undefined
  if (value1 == null && value2 == null) return true;
  if (value1 == null || value2 == null) return false;

  // Convert to strings for comparison
  const str1 = String(value1).toLowerCase().trim();
  const str2 = String(value2).toLowerCase().trim();

  // For phone fields, normalize before comparison
  if (fieldType === "PHONE" || fieldType === "telephone") {
    return normalizePhone(str1) === normalizePhone(str2);
  }

  // For email fields, normalize before comparison
  if (fieldType === "EMAIL" || fieldType === "email") {
    return normalizeEmail(str1) === normalizeEmail(str2);
  }

  // Default string comparison
  return str1 === str2;
}
