/**
 * Value Validation Utilities for Field Conversion
 * 
 * This module provides validation functions for different field types
 * according to the field-conversion-specification.md document.
 */

import type {
  ValidationRule,
  ValidationResult,
  APFieldType,
  CCFieldType,
  ConversionError
} from "./types";
import { ConversionErrorType } from "./types";

/**
 * Email validation regex pattern
 */
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

/**
 * Phone number validation regex pattern (allows digits, spaces, dashes, plus, parentheses)
 */
const PHONE_REGEX = /^[\d\s\-\+\(\)]+$/;

/**
 * Validation rules for different field types
 */
export const VALIDATION_RULES: ValidationRule[] = [
  {
    fieldType: "EMAIL",
    validate: (value: unknown): ValidationResult => {
      if (typeof value !== "string") {
        return {
          isValid: false,
          message: "Email value must be a string"
        };
      }
      
      if (value.trim() === "") {
        return {
          isValid: true,
          normalizedValue: "",
          message: "Empty email value"
        };
      }

      const isValid = EMAIL_REGEX.test(value);
      return {
        isValid,
        message: isValid ? undefined : "Invalid email format",
        normalizedValue: isValid ? value.toLowerCase().trim() : undefined
      };
    }
  },
  {
    fieldType: "PHONE",
    validate: (value: unknown): ValidationResult => {
      if (typeof value !== "string") {
        return {
          isValid: false,
          message: "Phone value must be a string"
        };
      }

      if (value.trim() === "") {
        return {
          isValid: true,
          normalizedValue: "",
          message: "Empty phone value"
        };
      }

      const isValid = PHONE_REGEX.test(value);
      return {
        isValid,
        message: isValid ? undefined : "Invalid phone number format",
        normalizedValue: isValid ? normalizePhone(value) : undefined
      };
    }
  },
  {
    fieldType: "NUMERICAL",
    validate: (value: unknown): ValidationResult => {
      if (typeof value === "number") {
        return {
          isValid: true,
          normalizedValue: value
        };
      }

      if (typeof value !== "string") {
        return {
          isValid: false,
          message: "Numerical value must be a string or number"
        };
      }

      if (value.trim() === "") {
        return {
          isValid: true,
          normalizedValue: "",
          message: "Empty numerical value"
        };
      }

      const numValue = Number(value);
      const isValid = !isNaN(numValue);
      return {
        isValid,
        message: isValid ? undefined : "Value must be numeric",
        normalizedValue: isValid ? numValue : undefined
      };
    }
  },
  {
    fieldType: "DATE",
    validate: (value: unknown): ValidationResult => {
      if (typeof value !== "string") {
        return {
          isValid: false,
          message: "Date value must be a string"
        };
      }

      if (value.trim() === "") {
        return {
          isValid: true,
          normalizedValue: "",
          message: "Empty date value"
        };
      }

      const dateValue = new Date(value);
      const isValid = !isNaN(dateValue.getTime());
      return {
        isValid,
        message: isValid ? undefined : "Invalid date format",
        normalizedValue: isValid ? normalizeDate(value) : undefined
      };
    }
  },
  {
    fieldType: "TEXT",
    validate: (value: unknown): ValidationResult => {
      if (typeof value !== "string") {
        return {
          isValid: false,
          message: "Text value must be a string"
        };
      }

      return {
        isValid: true,
        normalizedValue: value
      };
    }
  },
  {
    fieldType: "LARGE_TEXT",
    validate: (value: unknown): ValidationResult => {
      if (typeof value !== "string") {
        return {
          isValid: false,
          message: "Large text value must be a string"
        };
      }

      return {
        isValid: true,
        normalizedValue: value
      };
    }
  }
];

/**
 * Normalize phone number by removing formatting
 */
export function normalizePhone(phone: string): string {
  return phone.replace(/[^\d+]/g, "");
}

/**
 * Normalize email by converting to lowercase and trimming
 */
export function normalizeEmail(email: string): string {
  return email.toLowerCase().trim();
}

/**
 * Normalize date to ISO 8601 format (YYYY-MM-DD)
 */
export function normalizeDate(date: string): string {
  return new Date(date).toISOString().split('T')[0];
}

/**
 * Validate a value against field type rules
 */
export function validateFieldValue(
  value: unknown, 
  fieldType: APFieldType | CCFieldType
): ValidationResult {
  const rule = VALIDATION_RULES.find(r => r.fieldType === fieldType);
  
  if (!rule) {
    // Default validation for unknown field types
    return {
      isValid: true,
      normalizedValue: value,
      message: `No validation rule found for field type: ${fieldType}`
    };
  }

  return rule.validate(value);
}

/**
 * Validate array of values for multi-value fields
 */
export function validateMultipleValues(
  values: unknown[], 
  fieldType: APFieldType | CCFieldType
): ValidationResult {
  if (!Array.isArray(values)) {
    return {
      isValid: false,
      message: "Multi-value field requires an array of values"
    };
  }

  const validatedValues: unknown[] = [];
  const errors: string[] = [];

  for (let i = 0; i < values.length; i++) {
    const result = validateFieldValue(values[i], fieldType);
    
    if (result.isValid) {
      validatedValues.push(result.normalizedValue ?? values[i]);
    } else {
      errors.push(`Value ${i + 1}: ${result.message}`);
    }
  }

  return {
    isValid: errors.length === 0,
    normalizedValue: validatedValues,
    message: errors.length > 0 ? errors.join("; ") : undefined
  };
}

/**
 * Check if a value is empty or null
 */
export function isEmpty(value: unknown): boolean {
  if (value === null || value === undefined) {
    return true;
  }
  
  if (typeof value === "string") {
    return value.trim() === "";
  }
  
  if (Array.isArray(value)) {
    return value.length === 0;
  }
  
  if (typeof value === "object") {
    return Object.keys(value).length === 0;
  }
  
  return false;
}

/**
 * Create a validation error
 */
export function createValidationError(
  fieldId: string,
  fieldName: string,
  message: string,
  sourceValue?: unknown
): ConversionError {
  return {
    type: ConversionErrorType.VALIDATION_FAILED,
    message,
    fieldId,
    fieldName,
    sourceValue,
    recoverable: true,
    suggestions: ["Check the value format", "Ensure the value matches the field type requirements"]
  };
}

/**
 * Field type compatibility matrix for validation
 */
export const FIELD_COMPATIBILITY_MATRIX = {
  // AP → CC compatibility
  "TEXT": ["text", "textarea", "email", "telephone"],
  "LARGE_TEXT": ["textarea", "text"],
  "NUMERICAL": ["number", "text"],
  "PHONE": ["telephone", "text"],
  "EMAIL": ["email", "text"],
  "DATE": ["date", "text"],
  "MONETORY": ["text", "number"],
  "SINGLE_OPTIONS": ["select"],
  "MULTIPLE_OPTIONS": ["select"],
  "CHECKBOX": ["select"],
  "RADIO": ["select", "boolean"],
  "TEXTBOX_LIST": ["text", "textarea", "select"],

  // CC → AP compatibility
  "text": ["TEXT", "LARGE_TEXT", "TEXTBOX_LIST"],
  "textarea": ["LARGE_TEXT", "TEXT", "TEXTBOX_LIST"],
  "number": ["NUMERICAL", "TEXT", "TEXTBOX_LIST"],
  "telephone": ["PHONE", "TEXT", "TEXTBOX_LIST"],
  "email": ["EMAIL", "TEXT", "TEXTBOX_LIST"],
  "date": ["DATE", "TEXT", "TEXTBOX_LIST"],
  "select": ["SINGLE_OPTIONS", "MULTIPLE_OPTIONS", "TEXTBOX_LIST"],
  "select-or-custom": ["SINGLE_OPTIONS", "MULTIPLE_OPTIONS", "TEXTBOX_LIST"],
  "boolean": ["RADIO"],
  "medication": ["TEXT", "TEXTBOX_LIST"],
  "permanent-diagnoses": ["TEXT", "TEXTBOX_LIST"],
  "patient-has-recommended": ["TEXT", "TEXTBOX_LIST"]
} as const;

/**
 * Check if field types are compatible for conversion
 */
export function areFieldTypesCompatible(
  sourceType: APFieldType | CCFieldType,
  targetType: APFieldType | CCFieldType
): boolean {
  const compatibleTypes = FIELD_COMPATIBILITY_MATRIX[sourceType as keyof typeof FIELD_COMPATIBILITY_MATRIX];
  return compatibleTypes ? compatibleTypes.includes(targetType as never) : false;
}
