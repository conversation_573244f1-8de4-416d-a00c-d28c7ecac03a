/**
 * Value Conversion Types for Bidirectional Field Synchronization
 * 
 * This module defines the core types and interfaces for converting field values
 * between AutoPatient (AP) and CliniCore (CC) systems according to the
 * field-conversion-specification.md document.
 */

import type { GetCCCustomField, GetCCPatientCustomField } from "@/type/CCTypes";
import type { APGetCustomFieldType } from "@/type/APTypes";

/**
 * Supported field types for value conversion
 */
export type APFieldType = 
  | "TEXT" 
  | "LARGE_TEXT" 
  | "NUMERICAL" 
  | "PHONE" 
  | "EMAIL" 
  | "DATE" 
  | "MONETORY" 
  | "SINGLE_OPTIONS" 
  | "MULTIPLE_OPTIONS" 
  | "CHECKBOX" 
  | "RADIO" 
  | "TEXTBOX_LIST" 
  | "FILE_UPLOAD";

export type CCFieldType = 
  | "text" 
  | "textarea" 
  | "number" 
  | "telephone" 
  | "email" 
  | "date" 
  | "select" 
  | "select-or-custom" 
  | "boolean" 
  | "medication" 
  | "permanent-diagnoses" 
  | "patient-has-recommended";

/**
 * CC field value structure for multi-value fields
 */
export interface CCFieldValue {
  id?: number;
  value?: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number | null;
  updatedBy?: number | null;
}

/**
 * AP TEXTBOX_LIST field value structure
 */
export interface APTextboxListValue {
  field_value: Record<string, string>;
}

/**
 * AP custom field value structure
 */
export interface APCustomFieldValue {
  id: string;
  value?: string | number | string[];
  field_value?: Record<string, string>;
}

/**
 * Conversion direction for value transformation
 */
export type ConversionDirection = "cc_to_ap" | "ap_to_cc";

/**
 * Conversion context containing field information and metadata
 */
export interface ConversionContext {
  direction: ConversionDirection;
  sourceField: GetCCCustomField | APGetCustomFieldType;
  targetField: APGetCustomFieldType | GetCCCustomField;
  sourcePlatform: "cc" | "ap";
  targetPlatform: "ap" | "cc";
}

/**
 * Value conversion result
 */
export interface ConversionResult<T = unknown> {
  success: boolean;
  value?: T;
  error?: ConversionError;
  warnings?: string[];
  metadata?: {
    originalValue?: unknown;
    conversionType?: string;
    optionsMapped?: number;
    optionsSkipped?: number;
  };
}

/**
 * Error types for value conversion
 */
export const ConversionErrorType = {
  FIELD_TYPE_MISMATCH: "FIELD_TYPE_MISMATCH",
  INVALID_VALUE_FORMAT: "INVALID_VALUE_FORMAT",
  MISSING_FIELD_MAPPING: "MISSING_FIELD_MAPPING",
  API_ERROR: "API_ERROR",
  VALIDATION_FAILED: "VALIDATION_FAILED",
  OPTION_NOT_FOUND: "OPTION_NOT_FOUND",
  EMPTY_VALUE: "EMPTY_VALUE",
  UNSUPPORTED_CONVERSION: "UNSUPPORTED_CONVERSION"
} as const;

export type ConversionErrorType = typeof ConversionErrorType[keyof typeof ConversionErrorType];

/**
 * Conversion error details
 */
export interface ConversionError {
  type: ConversionErrorType;
  message: string;
  fieldId: string;
  fieldName: string;
  sourceValue?: unknown;
  suggestions?: string[];
  recoverable?: boolean;
}

/**
 * Validation rule for field values
 */
export interface ValidationRule {
  fieldType: string;
  validate: (value: unknown) => ValidationResult;
}

/**
 * Validation result
 */
export interface ValidationResult {
  isValid: boolean;
  message?: string;
  normalizedValue?: unknown;
}

/**
 * Option mapping for select/dropdown fields
 */
export interface OptionMapping {
  sourceValue: string;
  targetValue: string;
  sourceId?: string | number;
  targetId?: string | number;
  confidence: number;
}

/**
 * Boolean conversion options for international support
 */
export interface BooleanConversionOptions {
  yesValues: string[];
  noValues: string[];
  defaultYesValue: string;
  defaultNoValue: string;
}

/**
 * TEXTBOX_LIST conversion configuration
 */
export interface TextboxListConfig {
  generateOptionIds: boolean;
  preserveOrder: boolean;
  allowEmptyValues: boolean;
  maxOptions?: number;
}

/**
 * Multi-value field conversion strategy
 */
export type MultiValueStrategy = "textbox_list" | "multiple_options" | "pipe_separated";

/**
 * Field conversion configuration
 */
export interface FieldConversionConfig {
  multiValueStrategy: MultiValueStrategy;
  booleanOptions: BooleanConversionOptions;
  textboxListConfig: TextboxListConfig;
  validationRules: ValidationRule[];
  allowFallbackToText: boolean;
  preserveEmptyValues: boolean;
}

/**
 * Batch conversion request
 */
export interface BatchConversionRequest {
  conversions: Array<{
    sourceValue: unknown;
    context: ConversionContext;
    config?: Partial<FieldConversionConfig>;
  }>;
  config?: FieldConversionConfig;
}

/**
 * Batch conversion result
 */
export interface BatchConversionResult {
  results: ConversionResult[];
  summary: {
    total: number;
    successful: number;
    failed: number;
    warnings: number;
  };
  errors: ConversionError[];
}

/**
 * Standard field mapping for email/phone conversion
 */
export interface StandardFieldMapping {
  sourceField: string;
  targetField: string;
  sourcePlatform: "cc" | "ap";
  targetPlatform: "ap" | "cc";
  conversionType: "direct" | "normalized";
  notes?: string;
}

/**
 * Performance metrics for conversion operations
 */
export interface ConversionMetrics {
  totalFieldsProcessed: number;
  successfulConversions: number;
  failedConversions: number;
  averageProcessingTimeMs: number;
  errorsByType: Record<ConversionErrorType, number>;
  fieldTypeDistribution: Record<string, number>;
}

/**
 * Cache configuration for conversion operations
 */
export interface CacheConfig {
  fieldMappingsTTL: number;    // 1 hour
  picklistOptionsTTL: number;  // 30 minutes
  allowedValuesTTL: number;    // 30 minutes
  maxCacheSize: number;        // 1000 entries
}

/**
 * Conversion service configuration
 */
export interface ConversionServiceConfig {
  defaultConfig: FieldConversionConfig;
  cacheConfig: CacheConfig;
  batchSize: number;
  maxConcurrentBatches: number;
  retryAttempts: number;
  retryDelayMs: number;
}
