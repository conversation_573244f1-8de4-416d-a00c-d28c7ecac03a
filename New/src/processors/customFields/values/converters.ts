/**
 * Core Value Conversion Functions
 * 
 * This module implements the core value conversion algorithms according to
 * the field-conversion-specification.md document.
 */

import type {
  CCFieldValue,
  APTextboxListValue,
  APCustomFieldValue,
  ConversionResult,
  ConversionError,
  BooleanConversionOptions,
  OptionMapping,
  APFieldType,
  CCFieldType
} from "./types";
import { ConversionErrorType } from "./types";
import type { GetCCCustomField, APGetCustomFieldType } from "@/type";
import { validateFieldValue, validateMultipleValues, isEmpty, createValidationError } from "./validation";

/**
 * Default boolean conversion options for international support
 */
export const DEFAULT_BOOLEAN_OPTIONS: BooleanConversionOptions = {
  yesValues: ["yes", "ja", "true", "1", "y"],
  noValues: ["no", "nein", "false", "0", "n"],
  defaultYesValue: "Yes",
  defaultNoValue: "No"
};

/**
 * Convert CC multi-value field to AP TEXTBOX_LIST
 * 
 * This is the core conversion for all CC multi-value fields according to the spec:
 * "All CC multi-value fields convert to AP TEXTBOX_LIST (no pipe separator fallbacks)"
 */
export async function convertCCMultiValueToAPTextboxList(
  ccValues: CCFieldValue[],
  apField: APGetCustomFieldType
): Promise<ConversionResult<APTextboxListValue>> {
  try {
    // Filter out empty values
    const validValues = ccValues.filter(v => v.value && v.value.trim() !== "");

    if (validValues.length === 0) {
      return {
        success: true,
        value: { field_value: {} },
        warnings: ["No valid values found in CC multi-value field"]
      };
    }

    // Create option ID to value mapping from AP field's picklistOptions
    const optionMap = new Map<string, string>();
    
    if (apField.picklistOptions && Array.isArray(apField.picklistOptions)) {
      // Handle both string[] and object[] formats
      apField.picklistOptions.forEach((option, index) => {
        if (typeof option === "string") {
          optionMap.set(option.toLowerCase(), `opt_${index + 1}`);
        } else if (typeof option === "object" && option.id && option.label) {
          optionMap.set(option.label.toLowerCase(), option.id);
        }
      });
    }

    const fieldValue: Record<string, string> = {};
    const warnings: string[] = [];
    let optionsMapped = 0;
    let optionsSkipped = 0;

    // Convert CC values to AP field_value structure
    for (let i = 0; i < validValues.length; i++) {
      const ccValue = validValues[i];
      const value = ccValue.value!; // We know it exists because of the filter above
      const normalizedValue = value.toLowerCase();

      // Try to find matching option ID
      let optionId = optionMap.get(normalizedValue);

      if (!optionId) {
        // Generate option ID if not found in picklistOptions
        optionId = `opt_${i + 1}`;
        warnings.push(`Generated option ID for unmapped value: "${value}"`);
      }

      fieldValue[optionId] = value;
      optionsMapped++;
    }

    return {
      success: true,
      value: { field_value: fieldValue },
      warnings: warnings.length > 0 ? warnings : undefined,
      metadata: {
        originalValue: ccValues,
        conversionType: "cc_multi_to_ap_textbox_list",
        optionsMapped,
        optionsSkipped
      }
    };

  } catch (error) {
    return {
      success: false,
      error: {
        type: ConversionErrorType.API_ERROR,
        message: `Failed to convert CC multi-value to AP TEXTBOX_LIST: ${error instanceof Error ? error.message : String(error)}`,
        fieldId: apField.id,
        fieldName: apField.name,
        sourceValue: ccValues,
        recoverable: true
      }
    };
  }
}

/**
 * Convert AP TEXTBOX_LIST to CC multi-value field
 */
export function convertAPTextboxListToCCMultiValue(
  apValue: APTextboxListValue,
  apField: APGetCustomFieldType,
  ccField: GetCCCustomField
): ConversionResult<CCFieldValue[]> {
  try {
    if (!apValue.field_value || Object.keys(apValue.field_value).length === 0) {
      return {
        success: true,
        value: [],
        warnings: ["Empty TEXTBOX_LIST field_value"]
      };
    }

    // Create value to option mapping from AP field's picklistOptions
    const valueMap = new Map<string, string>();
    
    if (apField.picklistOptions && Array.isArray(apField.picklistOptions)) {
      apField.picklistOptions.forEach(option => {
        if (typeof option === "object" && option.id && option.label) {
          valueMap.set(option.id, option.label);
        }
      });
    }

    const ccValues: CCFieldValue[] = [];
    const warnings: string[] = [];

    // Convert AP field_value to CC values array
    Object.entries(apValue.field_value).forEach(([, value]) => {
      if (value && value.trim() !== "") {
        // Use the actual value, not the mapped label
        ccValues.push({
          value: value.trim()
        });
      }
    });

    // Validate against CC field's allowedValues if present
    if (ccField.allowedValues && ccField.allowedValues.length > 0) {
      const allowedSet = new Set(ccField.allowedValues.map(av => av.value.toLowerCase()));

      ccValues.forEach(ccValue => {
        const value = ccValue.value;
        if (value && !allowedSet.has(value.toLowerCase())) {
          warnings.push(`Value "${value}" not in CC field's allowedValues`);
        }
      });
    }

    return {
      success: true,
      value: ccValues,
      warnings: warnings.length > 0 ? warnings : undefined,
      metadata: {
        originalValue: apValue,
        conversionType: "ap_textbox_list_to_cc_multi",
        optionsMapped: ccValues.length
      }
    };

  } catch (error) {
    return {
      success: false,
      error: {
        type: ConversionErrorType.API_ERROR,
        message: `Failed to convert AP TEXTBOX_LIST to CC multi-value: ${error instanceof Error ? error.message : String(error)}`,
        fieldId: ccField.id.toString(),
        fieldName: ccField.name,
        sourceValue: apValue,
        recoverable: true
      }
    };
  }
}

/**
 * Convert CC boolean to AP RADIO
 */
export function convertCCBooleanToAPRadio(
  ccBoolean: boolean,
  options: BooleanConversionOptions = DEFAULT_BOOLEAN_OPTIONS
): ConversionResult<string> {
  try {
    const radioValue = ccBoolean ? options.defaultYesValue : options.defaultNoValue;
    
    return {
      success: true,
      value: radioValue,
      metadata: {
        originalValue: ccBoolean,
        conversionType: "cc_boolean_to_ap_radio"
      }
    };
  } catch (error) {
    return {
      success: false,
      error: {
        type: ConversionErrorType.INVALID_VALUE_FORMAT,
        message: `Failed to convert CC boolean to AP RADIO: ${error instanceof Error ? error.message : String(error)}`,
        fieldId: "",
        fieldName: "",
        sourceValue: ccBoolean,
        recoverable: true
      }
    };
  }
}

/**
 * Convert AP RADIO to CC boolean
 */
export function convertAPRadioToCCBoolean(
  radioValue: string,
  options: BooleanConversionOptions = DEFAULT_BOOLEAN_OPTIONS
): ConversionResult<boolean> {
  try {
    if (!radioValue || typeof radioValue !== "string") {
      return {
        success: false,
        error: {
          type: ConversionErrorType.INVALID_VALUE_FORMAT,
          message: "RADIO value must be a non-empty string",
          fieldId: "",
          fieldName: "",
          sourceValue: radioValue,
          recoverable: true
        }
      };
    }

    const normalizedValue = radioValue.toLowerCase().trim();
    
    if (options.yesValues.includes(normalizedValue)) {
      return {
        success: true,
        value: true,
        metadata: {
          originalValue: radioValue,
          conversionType: "ap_radio_to_cc_boolean"
        }
      };
    }
    
    if (options.noValues.includes(normalizedValue)) {
      return {
        success: true,
        value: false,
        metadata: {
          originalValue: radioValue,
          conversionType: "ap_radio_to_cc_boolean"
        }
      };
    }

    // Default to false for unrecognized values
    return {
      success: true,
      value: false,
      warnings: [`Unrecognized RADIO value "${radioValue}", defaulting to false`],
      metadata: {
        originalValue: radioValue,
        conversionType: "ap_radio_to_cc_boolean"
      }
    };

  } catch (error) {
    return {
      success: false,
      error: {
        type: ConversionErrorType.INVALID_VALUE_FORMAT,
        message: `Failed to convert AP RADIO to CC boolean: ${error instanceof Error ? error.message : String(error)}`,
        fieldId: "",
        fieldName: "",
        sourceValue: radioValue,
        recoverable: true
      }
    };
  }
}

/**
 * Create boolean RADIO options for AP fields
 */
export function createBooleanRadioOptions(
  options: BooleanConversionOptions = DEFAULT_BOOLEAN_OPTIONS
): string[] {
  return [options.defaultYesValue, "Ja", options.defaultNoValue, "Nein"];
}

/**
 * Convert single value between platforms with validation
 */
export function convertSingleValue(
  value: unknown,
  sourceFieldType: APFieldType | CCFieldType,
  targetFieldType: APFieldType | CCFieldType,
  fieldId: string,
  fieldName: string
): ConversionResult<unknown> {
  try {
    // Handle empty values
    if (isEmpty(value)) {
      return {
        success: true,
        value: "",
        warnings: ["Empty value converted to empty string"]
      };
    }

    // Validate source value
    const validation = validateFieldValue(value, sourceFieldType);
    if (!validation.isValid) {
      return {
        success: false,
        error: createValidationError(fieldId, fieldName, validation.message || "Validation failed", value)
      };
    }

    const normalizedValue = validation.normalizedValue ?? value;

    // Direct mapping for compatible types
    if (sourceFieldType === targetFieldType) {
      return {
        success: true,
        value: normalizedValue,
        metadata: {
          originalValue: value,
          conversionType: "direct_mapping"
        }
      };
    }

    // Type-specific conversions
    if (targetFieldType === "TEXT" || targetFieldType === "text") {
      // Convert any type to text
      return {
        success: true,
        value: String(normalizedValue),
        metadata: {
          originalValue: value,
          conversionType: "to_text"
        }
      };
    }

    // Default: return normalized value
    return {
      success: true,
      value: normalizedValue,
      metadata: {
        originalValue: value,
        conversionType: "normalized"
      }
    };

  } catch (error) {
    return {
      success: false,
      error: {
        type: ConversionErrorType.API_ERROR,
        message: `Failed to convert single value: ${error instanceof Error ? error.message : String(error)}`,
        fieldId,
        fieldName,
        sourceValue: value,
        recoverable: true
      }
    };
  }
}
