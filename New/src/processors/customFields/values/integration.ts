/**
 * Integration Test for Field Value Conversion System
 * 
 * This file provides integration tests to verify the complete field value
 * conversion system works correctly with the existing field mapping service.
 */

import type {
  GetCCCustomField,
  GetCCPatientCustomField,
  APGetCustomFieldType
} from "@/type";
import { mapCCtoAPField } from "../mapping";
import {
  FieldValueConversionService,
  getRecommendedAPFieldType,
  createBooleanRadioFieldConfig,
  createTextboxListFieldConfig,
  ConversionErrorType
} from "./index";
import { logInfo, logWarn, logError } from "@/utils/logger";

/**
 * Integration test: Complete CC to AP field conversion workflow
 */
export async function testCompleteConversionWorkflow() {
  logInfo("Starting complete conversion workflow test");

  try {
    // Step 1: CC field with multi-value data
    const ccField: GetCCCustomField = {
      id: 123,
      name: "patient_allergies",
      label: "Patient Allergies",
      type: "text",
      allowMultipleValues: true,
      validation: "",
      color: null,
      positions: [],
      useCustomSort: null,
      isRequired: false,
      allowedValues: [
        { id: 1, value: "Peanuts", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
        { id: 2, value: "Shellfish", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null },
        { id: 3, value: "Dairy", createdAt: null, updatedAt: null, createdBy: null, updatedBy: null }
      ],
      defaultValues: []
    };

    // Step 2: Get field mapping recommendation
    const mappingResult = mapCCtoAPField(ccField);
    logInfo("Field mapping result", { mappingResult });

    // Step 3: Get recommended AP field type
    const recommendedType = getRecommendedAPFieldType(ccField);
    logInfo("Recommended AP field type", { recommendedType });

    // Step 4: Create AP field configuration
    const apFieldConfig = createTextboxListFieldConfig(
      "Patient Allergies",
      ["Peanuts", "Shellfish", "Dairy", "Latex", "Eggs"]
    );
    logInfo("AP field configuration", { apFieldConfig });

    // Step 5: Simulate AP field (as it would exist after creation)
    const apField: APGetCustomFieldType = {
      id: "ap_field_123",
      name: "Patient Allergies",
      dataType: "TEXTBOX_LIST",
      picklistOptions: [
        { id: "opt1", label: "Peanuts", prefillValue: "Peanuts" },
        { id: "opt2", label: "Shellfish", prefillValue: "Shellfish" },
        { id: "opt3", label: "Dairy", prefillValue: "Dairy" }
      ],
      textBoxListOptions: [
        { label: "Allergy 1", prefillValue: "Peanuts" },
        { label: "Allergy 2", prefillValue: "Shellfish" },
        { label: "Allergy 3", prefillValue: "Dairy" }
      ]
    };

    // Step 6: CC patient field with actual values
    const ccPatientField: GetCCPatientCustomField = {
      id: 456,
      values: [
        { id: 1, value: "Peanuts", createdAt: "2024-01-01", updatedAt: "2024-01-01", createdBy: 1, updatedBy: 1 },
        { id: 2, value: "Shellfish", createdAt: "2024-01-01", updatedAt: "2024-01-01", createdBy: 1, updatedBy: 1 }
      ],
      field: ccField,
      patient: 789
    };

    // Step 7: Perform value conversion using new simplified API
    const conversionService = new FieldValueConversionService();
    const conversionResult = await conversionService.convertCCToAP(ccPatientField);

    logInfo("Value conversion result", { conversionResult });

    // Step 8: Validate results
    if (conversionResult.success) {
      const apValue = conversionResult.value;
      if (apValue?.field_value) {
        const convertedValues = Object.values(apValue.field_value);
        logInfo("Successfully converted values", { 
          originalCount: ccPatientField.values.length,
          convertedCount: convertedValues.length,
          values: convertedValues
        });
        
        // Verify expected values are present
        const expectedValues = ["Peanuts", "Shellfish"];
        const hasAllExpected = expectedValues.every(expected => 
          convertedValues.includes(expected)
        );
        
        if (hasAllExpected) {
          logInfo("✅ All expected values converted successfully");
          return { success: true, result: conversionResult };
        } else {
          logWarn("⚠️ Some expected values missing from conversion");
          return { success: false, error: "Missing expected values" };
        }
      } else {
        logError("❌ Conversion succeeded but no field_value found");
        return { success: false, error: "No field_value in result" };
      }
    } else {
      logError("❌ Value conversion failed", { error: conversionResult.error });
      return { success: false, error: conversionResult.error };
    }

  } catch (error) {
    logError("Integration test failed", { error: error instanceof Error ? error.message : String(error) });
    return { success: false, error: String(error) };
  }
}

/**
 * Integration test: Boolean field conversion workflow
 */
export async function testBooleanConversionWorkflow() {
  logInfo("Starting boolean conversion workflow test");

  try {
    // Step 1: CC boolean field
    const ccField: GetCCCustomField = {
      id: 456,
      name: "is_smoker",
      label: "Is Patient a Smoker?",
      type: "boolean",
      allowMultipleValues: false,
      validation: "",
      color: null,
      positions: [],
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: []
    };

    // Step 2: Create AP RADIO field configuration
    const radioConfig = createBooleanRadioFieldConfig("Smoker Status");
    logInfo("Boolean RADIO configuration", { radioConfig });

    // Step 3: Simulate AP RADIO field
    const apField: APGetCustomFieldType = {
      id: "ap_field_456",
      name: "Smoker Status",
      dataType: "RADIO",
      picklistOptions: ["Yes", "Ja", "No", "Nein"]
    };

    // Step 4: CC patient field with boolean value
    const ccPatientField: GetCCPatientCustomField = {
      id: 789,
      values: [
        { id: 1, value: "true", createdAt: "2024-01-01", updatedAt: "2024-01-01", createdBy: 1, updatedBy: 1 }
      ],
      field: ccField,
      patient: 123
    };

    // Step 5: Perform conversion
    const conversionService = new FieldValueConversionService();
    const result = await conversionService.convertCCToAP(ccField, ccPatientField, apField);

    logInfo("Boolean conversion result", { result });

    // Step 6: Validate result
    if (result.success && result.value?.value === "Yes") {
      logInfo("✅ Boolean conversion successful");
      return { success: true, result };
    } else {
      logError("❌ Boolean conversion failed or unexpected value");
      return { success: false, error: "Unexpected conversion result" };
    }

  } catch (error) {
    logError("Boolean conversion test failed", { error: error instanceof Error ? error.message : String(error) });
    return { success: false, error: String(error) };
  }
}

/**
 * Integration test: Error handling and validation
 */
export async function testErrorHandling() {
  logInfo("Starting error handling test");

  try {
    const conversionService = new FieldValueConversionService();

    // Test 1: Invalid field type conversion
    const invalidCCField: GetCCCustomField = {
      id: 999,
      name: "invalid_field",
      label: "Invalid Field",
      type: "unknown_type" as any,
      allowMultipleValues: false,
      validation: "",
      color: null,
      positions: [],
      useCustomSort: null,
      isRequired: false,
      allowedValues: [],
      defaultValues: []
    };

    const invalidPatientField: GetCCPatientCustomField = {
      id: 888,
      values: [{ value: "test_value" }],
      field: invalidCCField,
      patient: 777
    };

    const apField: APGetCustomFieldType = {
      id: "ap_field_999",
      name: "Test Field",
      dataType: "TEXT"
    };

    const result = await conversionService.convertCCToAP(invalidPatientField);

    logInfo("Error handling test result", { result });

    // Should handle gracefully even with unknown field type
    if (result.success || (result.error && result.error.recoverable)) {
      logInfo("✅ Error handling working correctly");
      return { success: true, result };
    } else {
      logError("❌ Error handling failed");
      return { success: false, error: "Error handling not working" };
    }

  } catch (error) {
    logError("Error handling test failed", { error: error instanceof Error ? error.message : String(error) });
    return { success: false, error: String(error) };
  }
}

/**
 * Run all integration tests
 */
export async function runIntegrationTests() {
  logInfo("=== Starting Field Value Conversion Integration Tests ===");

  const results = {
    completeWorkflow: await testCompleteConversionWorkflow(),
    booleanConversion: await testBooleanConversionWorkflow(),
    errorHandling: await testErrorHandling()
  };

  const successCount = Object.values(results).filter(r => r.success).length;
  const totalCount = Object.keys(results).length;

  logInfo("=== Integration Test Results ===", {
    passed: successCount,
    total: totalCount,
    success: successCount === totalCount,
    results
  });

  return {
    success: successCount === totalCount,
    passed: successCount,
    total: totalCount,
    results
  };
}

// Export individual tests for selective running
export {
  testCompleteConversionWorkflow as completeWorkflow,
  testBooleanConversionWorkflow as booleanConversion,
  testErrorHandling as errorHandling
};
